import device from '../models/device.js';
import mongoose from 'mongoose';

export const getDevices = async (req, res) => {
    let filter={}
    if(req.query.userId){
     filter={userId:req.query.userId.split(',')}
    }
    let data = await device.find(filter).populate({
        path: 'reviews.customerId',
        select: 'FirstName LastName profile_pic'
    }).populate({
        path: 'favorites.customerId',
        select: 'FirstName LastName profile_pic'
    });
    res.send(data);
}

export const getDevicespatronpal = async (req, res) => {
    let filter={}
    if(req.query.userId){
     filter={userId:req.query.userId.split(',')}
    }
    let data = await device.find(filter).populate("userId").populate({
        path: 'reviews.customerId',
        select: 'FirstName LastName profile_pic'
    }).populate({
        path: 'favorites.customerId',
        select: 'FirstName LastName profile_pic'
    })
    .exec();
    res.send(data);
}

export const getDevice = async (req, res) => {
    let data = await device.findOne(req.params).populate({
        path: 'reviews.customerId',
        select: 'FirstName LastName profile_pic'
    }).populate({
        path: 'favorites.customerId',
        select: 'FirstName LastName profile_pic'
    });
    res.send(data);
}

export const postDevice = async (req, res) => {
    const { active, name, userId, Line1, Line2, City, Phoneno, State, PostalCode, Country,delivery, deliveryStartTime, deliveryEndTime, ChargesperKm, ChargesFreeKm, pickupStartTime, pickupEndTime, businessType , Streetaddress} = req.body;
    const image = req.file ? req.file.location : null
    const data = await new device({ active, name, userId, Line1, Line2, City, Phoneno, State, PostalCode, Country, image, delivery, deliveryStartTime, deliveryEndTime, ChargesperKm, ChargesFreeKm, pickupStartTime, pickupEndTime, businessType ,Streetaddress});
    await data.save().then(result => {
        console.log(result, "Device data save to database")
        res.json({
            _id: result._id,
            name: result.name,
            active: result.active,
            userId: result.userId,
            image: result.image,
            Line1: result.Line1,
            line2: result.Line2,
            City: result.City,
            Phoneno: result.Phoneno,
            State: result.State,
            PostalCode: result.PostalCode,
            Country:result.Country,
            delivery: result.delivery,
            deliveryStartTime: result.deliveryStartTime,
            deliveryEndTime: result.deliveryEndTime,
            ChargesperKm: result.ChargesperKm, 
            ChargesFreeKm: result.ChargesFreeKm,
            pickupStartTime: result.pickupStartTime,
            pickupEndTime: result.pickupEndTime,
            businessType: result.businessType,
            Streetaddress: result.Streetaddress,
        })
    }).catch(err => {
        res.status(400).send('unable to save database');
        console.log(err)
    })
}
export const updateDevice = async (req, res) => {
    console.log(req.params._id)
    let image
    if(req.file){
         image = req.file ? req.file.location : null
    }
    console.log("body /: ", req.body)

    let data = await device.findByIdAndUpdate(
        { _id: req.params._id },
        {
            $set: req.body, image: image,
        },  { new: true });
    if (data) {
        res.send({ data, message: "device data updated successfully" });
    }
    else {
        res.send({ message: "device data cannot be updated successfully" })
    }
}

// Get device information for review form by userId
export const getDeviceForReview = async (req, res) => {
    const { userId } = req.params;

    // Validate the userId
    if (!mongoose.Types.ObjectId.isValid(userId)) {
        return res.status(400).json({ message: "Invalid user ID format." });
    }

    try {
        // Find the device by userId
        const Device = await device.findOne({ userId }).populate('userId', 'name email businessName');
        if (!Device) {
            return res.status(404).json({ message: "Device not found for this user" });
        }

        // Return device information needed for review form
        const deviceInfo = {
            _id: Device._id,
            name: Device.name,
            businessName: Device.userId?.businessName || Device.name,
            address: `${Device.Line1 || ''} ${Device.Line2 || ''}, ${Device.City || ''}, ${Device.State || ''} ${Device.PostalCode || ''}`.trim(),
            phone: Device.Phoneno,
            image: Device.image,
            userId: Device.userId._id,
            userEmail: Device.userId.email
        };

        res.json({
            success: true,
            device: deviceInfo,
            message: "Device information retrieved successfully"
        });
    } catch (error) {
        console.error("Error getting device for review:", error);
        res.status(500).json({ message: "Server error" });
    }
};

// Handle review submission from email form
export const submitEmailReview = async (req, res) => {
    try {
        console.log('Email review request body:', req.body);
        const { deviceId, customerId, orderId, rating, testimonial } = req.body;

        if (!deviceId || !customerId) {
            return res.status(400).send(`
                <html>
                <head><title>Review Submission Error</title></head>
                <body style="font-family: Arial, sans-serif; padding: 40px; text-align: center;">
                    <h2 style="color: #dc3545;">❌ Error</h2>
                    <p>Device ID and Customer ID are required.</p>
                    <p><a href="javascript:history.back()" style="color: #068af5;">Go Back</a></p>
                </body>
                </html>
            `);
        }

        // Validate that rating is provided and is valid
        if (!rating || rating === '' || rating === '0') {
            return res.status(400).send(`
                <html>
                <head><title>Review Submission Error</title></head>
                <body style="font-family: Arial, sans-serif; padding: 40px; text-align: center;">
                    <h2 style="color: #dc3545;">❌ Error</h2>
                    <p>Please select a star rating before submitting your review.</p>
                    <p><a href="javascript:history.back()" style="color: #068af5;">Go Back</a></p>
                </body>
                </html>
            `);
        }

        // Validate rating is between 1-5
        const ratingNum = parseInt(rating);
        console.log('Rating received:', rating, 'Parsed to:', ratingNum, 'Type:', typeof ratingNum);

        if (isNaN(ratingNum) || ratingNum < 1 || ratingNum > 5) {
            return res.status(400).send(`
                <html>
                <head><title>Review Submission Error</title></head>
                <body style="font-family: Arial, sans-serif; padding: 40px; text-align: center;">
                    <h2 style="color: #dc3545;">❌ Error</h2>
                    <p>Rating must be between 1 and 5 stars.</p>
                    <p><a href="javascript:history.back()" style="color: #068af5;">Go Back</a></p>
                </body>
                </html>
            `);
        }

        // Find the device
        const Device = await device.findById(deviceId);
        if (!Device) {
            return res.status(404).send(`
                <html>
                <head><title>Review Submission Error</title></head>
                <body style="font-family: Arial, sans-serif; padding: 40px; text-align: center;">
                    <h2 style="color: #dc3545;">❌ Error</h2>
                    <p>Restaurant not found.</p>
                    <p><a href="javascript:history.back()" style="color: #068af5;">Go Back</a></p>
                </body>
                </html>
            `);
        }

        // Check if a review from the same customer already exists
        const existingReview = Device.reviews.find(review => review.customerId && review.customerId.toString() === customerId);
        if (existingReview) {
            return res.status(400).send(`
                <html>
                <head><title>Review Already Submitted</title></head>
                <body style="font-family: Arial, sans-serif; padding: 40px; text-align: center;">
                    <h2 style="color: #ffc107;">⚠️ Already Reviewed</h2>
                    <p>You have already submitted a review for this restaurant.</p>
                    <p>Thank you for your previous feedback!</p>
                </body>
                </html>
            `);
        }

        // Create the new review object with all required fields
        const newReview = {
            rating: ratingNum, // Use the validated rating number
            testimonial: testimonial || '',
            customerId: new mongoose.Types.ObjectId(customerId), // Convert to ObjectId
            orderId: orderId || '',
            createdDate: new Date()
        };

        console.log('Creating review with data:', newReview);

        // Add the review to the device and save
        Device.reviews.push(newReview);

        // Validate the device before saving
        const validationError = Device.validateSync();
        if (validationError) {
            console.error('Validation error:', validationError);
            throw validationError;
        }

        await Device.save();

        // Return success page
        const restaurantName = Device.name || Device.businessName || 'Restaurant';
        const starsDisplay = '⭐'.repeat(ratingNum);

        res.send(`
            <html>
            <head>
                <title>Review Submitted Successfully</title>
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
            </head>
            <body style="font-family: 'Poppins', Arial, sans-serif; padding: 40px; text-align: center; background-color: #f8f9fa;">
                <div style="max-width: 600px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                    <h1 style="color: #068af5; margin-bottom: 20px;">✅ Thank You!</h1>
                    <h2 style="color: #333; margin-bottom: 30px;">Your Review Has Been Submitted Successfully</h2>

                    <div style="background-color: #f0f8ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
                        <h3 style="color: #068af5; margin-top: 0;">Review Summary for ${restaurantName}</h3>
                        <p style="margin: 10px 0;"><strong>Your Rating:</strong></p>
                        <div style="font-size: 24px; margin: 15px 0;">${starsDisplay} (${ratingNum}/5)</div>
                        ${testimonial ? `<p style="margin: 15px 0;"><strong>Your Comment:</strong><br>"${testimonial}"</p>` : ''}
                    </div>

                    <p style="color: #666; font-size: 16px; line-height: 1.6;">
                        Your feedback is valuable to us and helps other customers make informed decisions.
                        We appreciate you taking the time to share your experience!
                    </p>

                    <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
                        <p style="color: #999; font-size: 14px;">
                            Thank you for choosing ${restaurantName}!<br>
                            We look forward to serving you again.
                        </p>
                    </div>
                </div>
            </body>
            </html>
        `);

    } catch (error) {
        console.error("Error in submitEmailReview:", error);
        res.status(500).send(`
            <html>
            <head><title>Review Submission Error</title></head>
            <body style="font-family: Arial, sans-serif; padding: 40px; text-align: center;">
                <h2 style="color: #dc3545;">❌ Server Error</h2>
                <p>There was an error submitting your review. Please try again later.</p>
                <p><a href="javascript:history.back()" style="color: #068af5;">Go Back</a></p>
            </body>
            </html>
        `);
    }
};

export const postReview = async (req, res) => {
    const { deviceId, food, service, ambiance, testimonial,  customerId } = req.body;

    // Validate the deviceId
    if (!mongoose.Types.ObjectId.isValid(deviceId)) {
        return res.status(400).json({ message: "Invalid device ID format." });
    }

    try {
        // Find the device by ID
        const Device = await device.findById(deviceId);
        if (!Device) {
            return res.status(404).json({ message: "Device not found" });
        }

        // Check if a review from the same customer already exists
        const existingReview = Device.reviews.find(review => review.customerId == customerId);
        if (existingReview) {
            return res.status(400).json({ message: "Review from this customer already exists." });
        }

        // Create and add the new review
        const newReview = {
            food,
            service,
            ambiance,
            testimonial,
            customerId
        };

        Device.reviews.push(newReview);
        await Device.save();

        res.json({ message: "Review added successfully", review: newReview });
    } catch (error) {
        console.error("Error adding review:", error);
        res.status(500).json({ message: "Server error" });
    }
};


// Get all devices where any review has the given customerId    
export const getallcustomer = async (req, res) => {
    const { customerId } = req.params;

    // Validate customerId
    if (!mongoose.Types.ObjectId.isValid(customerId)) {
        return res.status(400).json({ message: "Invalid customer ID format." });
    }

    try {
        // Find all devices where reviews contain the given customerId
        let devices = await device.find({
            'reviews.customerId': customerId
        }).populate({
            path: 'reviews.customerId',
            select: 'FirstName LastName profile_pic' // Adjust as needed to include necessary fields
        });

        // Filter reviews to only include those with the matching customerId
        devices = devices.map(device => {
            device.reviews = device.reviews.filter(review => {
                return review.customerId._id.toString() === customerId;
            });
            return device;
        });

        res.json(devices);
    } catch (error) {
        console.error("Error fetching devices:", error);
        res.status(500).json({ message: "Server error" });
    }
};
// deviceRouter.put('/:deviceId/favorites/:customerId',
export const addFavorite = async (req, res) => {
    try {
      const { deviceId, customerId } = req.params; // Destructure params for clarity
      
      console.log("deviceId, customerId : " , deviceId, customerId);
      // Validate required parameters
      if (!deviceId || !customerId) {
        return res.status(400).json({ error: 'Missing required parameters: deviceId and customerId' });
      }

      const Device = await device.findById(deviceId); // Find the device by ID
  
      if (!Device) {
        return res.status(404).json({ error: 'Device not found' });
      }
  
      const existingFavorite = Device.favorites.some(favorite => favorite.customerId.toString() == customerId);
  
      if (existingFavorite) {
        // If the device is already in favorites, remove it
        Device.favorites = Device.favorites.filter(favorite => favorite.customerId.toString() !== customerId);
      } else {
          
          // If the device is not in favorites, add it
          Device.favorites.push({ customerId: customerId });
        }
        console.log("Device : ",Device);
        
      await Device.save(); // Save the changes to the device
  
      const updatedDevice = await device.findById(Device).populate('favorites.customerId'); // Populate customer details
  
      res.status(200).json({ status: true, data: updatedDevice });
    } catch (error) {
      console.error(error); // Log the error for debugging
      res.status(500).json({ error: 'Internal server error' });
    }
  };
  
  export const getFavoriteByCutomerId = async (req, res) => {
    try {
      const { customerId } = req.params;
  
      // Validate required parameter
      if (!customerId) {
        return res.status(400).json({ error: 'Missing required parameter: customerId' });
      }
  
      const Devices = await device.find().populate('favorites.customerId');
      console.log("Devices : ",Devices);
      
      const favoriteDevices = Devices.filter(device => device.favorites.some(favorite =>   favorite?.customerId?._id.toString() === customerId));
  
      res.json(favoriteDevices);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal server error' });
    }
  };

// export const getallcustomer = async (req, res) => {
//     const { customerId } = req.params;

//     // Validate customerId
//     if (!mongoose.Types.ObjectId.isValid(customerId)) {
//         return res.status(400).json({ message: "Invalid customer ID format." });
//     }

//     try {
//         // Find all devices where reviews contain the given customerId
//         let devices = await device.find({
//             'reviews.customerId': customerId
//         }).populate({
//             path: 'reviews.customerId',
//             select: 'FirstName LastName profile_pic' // Adjust as needed to include necessary fields
//         });

//         // Filter reviews to only include those with the matching customerId
//         devices = devices.map(device => {
//             device.reviews = device.reviews
//                 .filter(review => review.customerId._id.toString() === customerId)
//                 .map(review => {
//                     // Clone the review object to avoid mutating Mongoose documents directly
//                     const clonedReview = {
//                         ...review.toObject(),
//                         // Calculate the total points and average score
//                         averageScore: ((review.food + review.service + review.ambiance) / 3).toFixed(2)
//                     };
//                     return clonedReview;
//                 });

//             return device;
//         });

//         res.json(devices);
//     } catch (error) {
//         console.error("Error fetching devices:", error);
//         res.status(500).json({ message: "Server error" });
//     }
// };




export const deleteDevice = async (req, res) => {
    console.log(req.params)
    let data = await device.deleteOne(req.params)
    if (data) {
        res.send({ message: "device data delete successfully" });
    }
    else {
        res.send({ message: "device data cannot delete successfully" })
    }
}