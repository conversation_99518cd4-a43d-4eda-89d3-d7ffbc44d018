import express from "express";
import { awsupload } from '../middlewares/aws-s3-upload.js'
const routes = express.Router();

import {
    getDevices,
    getDevice,
    postDevice,
    updateDevice,
    deleteDevice,
    getDevicespatronpal,
    postReview,
    getDeviceForReview,
    submitEmailReview,
    showReviewPage,
    getallcustomer,
    addFavorite,
    getFavoriteByCutomerId
} from "../api/device.js"

routes.get('/device', getDevices)
routes.get('/Pdevice', getDevicespatronpal)
routes.get('/device/:_id', getDevice)
routes.get('/device/review/:userId', getDeviceForReview)
routes.get('/device/review-page', showReviewPage)
routes.post('/device/review', postReview)
routes.post('/device/email-review', submitEmailReview)
routes.get('/devices/customer/:customerId', getallcustomer)

routes.post('/device/:deviceId/favorite/:customerId', addFavorite)
routes.get('/devices/favorites/:customerId', getFavoriteByCutomerId)

routes.post('/device', awsupload.single('image'), postDevice)
routes.put('/device/:_id', awsupload.single('image'), updateDevice)
routes.delete('/device/:_id', deleteDevice)


export default routes