import product from '../models/product.js';
import reviewModel from '../models/Review.js';
import mongoose from 'mongoose';

// Get reviews for a specific product
export const getReviewsByProductId = async (req, res) => {
  try {
    const { productId } = req.params;
    const limit = parseInt(req.query.limit) || 10;

    console.log('getReviewsByProductId called with productId:', productId);

    // Convert productId to ObjectId
    const productObjectId = new mongoose.Types.ObjectId(productId);
    console.log('Converted to ObjectId:', productObjectId);

    // Find reviews for the product and populate customer information
    const reviews = await reviewModel
      .find({ productId: productObjectId })
      .populate('customerId', 'FirstName LastName Email')
      .sort({ creatdateFormat: -1 }) // Sort by creation date descending (most recent first)
      .limit(limit)
      .lean();

    // Calculate average rating
    const totalReviews = await reviewModel.countDocuments({ productId: productObjectId });
    const avgResult = await reviewModel.aggregate([
      { $match: { productId: productObjectId } },
      { $group: { _id: null, averageRating: { $avg: '$rating' } } }
    ]);

    const averageRating = avgResult.length > 0 ? avgResult[0].averageRating : 0;

    console.log('Found reviews:', reviews.length);
    console.log('Total reviews:', totalReviews);
    console.log('Average rating:', averageRating);

    res.json({
      reviews,
      totalReviews,
      averageRating: Math.round(averageRating * 10) / 10 // Round to 1 decimal place
    });
  } catch (error) {
    console.error('Error fetching reviews:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

export const postReview = async (req, res) => {
  try {
    const { customerId, rating, review, productId , Profession ,likes} = req.body;

    const ReviewData = new reviewModel({ customerId, rating, review, productId , Profession , likes});
    const savedReview = await ReviewData.save();
    res.status(201).json({
      _id: savedReview._id,
      customerId: savedReview.customerId,
      rating: savedReview.rating,
      review: savedReview.review,
      productId: savedReview.productId,
      Profession:savedReview.Profession,
      likes:savedReview.likes


    });
    // Update the Product document with the review _id
    const updatedProduct = await product.findOneAndUpdate(
      { _id: productId },  // Make sure this matches the structure of your request body 
      {
        $push: { reviewId: savedReview._id },
      },
      { new: true }
    );

    if (!updatedProduct) {
      return res.status(404).send('Product not found');
    }

   
  } catch (error) {
    console.error('Error:', error);
    res.status(400).send('Unable to save rating and review.');
  }
};


// Like review API
export const updateReview = async (req, res) => {
  try {
    const { reviewId } = req.params;

    // Find the review by ID
    const review = await reviewModel.findById(reviewId);

    if (!review) {
      return res.status(404).send('Review not found');
    }
 
    // Increment the like count (assuming 'likes' is a field in your Review model)
    review.likes = (review.likes || 0) + 1;

    // Save the updated review
    await review.save();

    // Optionally, update the product or any other related data

    res.json({ message: 'Review liked successfully', updatedReview: review });

  } catch (error) {
    console.error('Error:', error);
    res.status(500).send('Internal Server Error');
  }
};


export const deleteReview = async (req, res) => {
  try {
    const { reviewId } = req.params;
    console.log('Received params:', req.params);

    // Attempt to delete the review
    const data = await reviewModel.deleteOne({ _id: reviewId });

    // Check if a document was deleted
    if (data.deletedCount > 0) {
      res.send({ message: 'Review deleted successfully' });
    } else {
      res.status(404).send({ message: 'Review data not found or cannot be deleted' });
    }
  } catch (error) {
    console.error('Error:', error);
    res.status(500).send('Internal Server Error');
  }
};