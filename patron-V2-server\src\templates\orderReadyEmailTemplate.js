// Email template for order completion notifications with embedded review form
export const generateOrderReadyEmailTemplate = (
  customerName,
  orderNo,
  businessName,
  deviceInfo,
  apiBaseUrl,
  orderId,
  customerId
) => {
  // Template for order completion email with review request

  return `
  <html>
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <style>
        body {
          font-family: 'Poppins', sans-serif;
          background-color: #f8f8f8;
          margin: 0;
          padding: 0;
        }
        .container {
          width: 90%;
          max-width: 600px;
          margin: 0 auto;
          background: #ffffff;
          padding: 30px;
          border-radius: 10px;
          box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header-bar {
          height: 4rem;
          background-color: #068af5;
          border-top-left-radius: 10px;
          border-top-right-radius: 10px;
        }
        .logo {
          display: block;
          margin: 30px auto;
          max-width: 180px;
        }
        .order-details {
          background-color: #e8f4fd;
          padding: 15px;
          border-left: 4px solid #068af5;
          margin: 20px 0;
          border-radius: 5px;
        }
        .order-details h3 {
          margin: 0 0 10px;
          color: #068af5;
        }
        .review-section {
          background-color: #f0f8ff;
          padding: 25px;
          border-radius: 8px;
          border: 2px solid #068af5;
          margin: 30px 0;
        }
        .review-form {
          background-color: #fff;
          padding: 20px;
          border-radius: 8px;
          border: 1px solid #ddd;
        }
        .rating-label {
          font-weight: bold;
          margin-bottom: 10px;
          display: block;
          color: #333;
        }
        .star-rating {
          text-align: center;
          margin-bottom: 20px;
          direction: rtl;
          display: inline-block;
        }
        .star-rating input[type="radio"] {
          display: none;
        }
        .star-rating label {
          font-size: 35px;
          color: #ddd;
          cursor: pointer;
          margin: 0 2px;
          transition: color 0.2s;
          direction: ltr;
          display: inline-block;
        }
        .star-rating input[type="radio"]:checked ~ label,
        .star-rating label:hover,
        .star-rating label:hover ~ label {
          color: #ffc107;
        }
        .star-rating input[type="radio"]:checked ~ label {
          color: #ffc107;
        }

        #selectedRating {
          display: none;
        }
        .form-group {
          margin-bottom: 15px;
        }
        .form-label {
          display: block;
          font-weight: bold;
          margin-bottom: 5px;
          color: #333;
        }
        .form-textarea {
          width: 100%;
          padding: 10px;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 14px;
          min-height: 80px;
          resize: vertical;
        }
        .submit-button {
          background-color: #068af5;
          color: white;
          padding: 12px 30px;
          border: none;
          border-radius: 5px;
          font-weight: bold;
          cursor: pointer;
          font-size: 16px;
          transition: background-color 0.3s;
        }
        .submit-button:hover {
          background-color: #0056b3;
        }
        .footer {
          text-align: center;
          font-size: 13px;
          color: #888;
          margin-top: 30px;
        }
        .social-icons img {
          width: 20px;
          margin: 0 5px;
        }
        @media screen and (max-width: 600px) {
          .container {
            padding: 20px;
          }
          .logo {
            max-width: 140px;
          }
        }
      </style>
      <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap" rel="stylesheet">
    </head>
    <body>
      <div class="header-bar"></div>
      <div class="container">
        <img class="logo" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690281194/WhatsApp_Image_2023-07-25_at_3.32.42_PM_gswsmd.jpg" alt="Logo" />

        <p>Dear <strong>${customerName}</strong>,</p>

        <div class="order-details">
          <h3>🎉 Thank You! Your Order is Complete</h3>
          <p><strong>Order Number:</strong> ${orderNo}</p>
          <p><strong>Status:</strong> Your order has been <strong>completed</strong>.</p>
          <p>We hope you enjoyed your experience with us!</p>
        </div>

        <p>Thank you for choosing <strong>${businessName}</strong>. We hope you enjoyed your meal!</p>

        <div class="review-section">
          <h3>📝 We Value Your Feedback!</h3>
          <p>Your thoughts help us serve you better. Please click on the stars below to rate your experience:</p>

          <div class="star-rating-links" style="text-align: center; margin: 30px 0;">
            <p style="font-weight: bold; margin-bottom: 15px;">⭐ Rate Your Overall Experience:</p>
            <div style="display: flex; justify-content: center; gap: 10px; flex-wrap: wrap;">
              <a href="${apiBaseUrl}/api/v1/device/review-page?deviceId=${deviceInfo?._id || ''}&customerId=${customerId}&orderId=${orderId}&rating=1"
                 style="display: inline-block; padding: 10px 15px; background-color: #ffc107; color: #333; text-decoration: none; border-radius: 5px; font-size: 18px; margin: 5px;">
                ⭐ (1 Star)
              </a>
              <a href="${apiBaseUrl}/api/v1/device/review-page?deviceId=${deviceInfo?._id || ''}&customerId=${customerId}&orderId=${orderId}&rating=2"
                 style="display: inline-block; padding: 10px 15px; background-color: #ffc107; color: #333; text-decoration: none; border-radius: 5px; font-size: 18px; margin: 5px;">
                ⭐⭐ (2 Stars)
              </a>
              <a href="${apiBaseUrl}/api/v1/device/review-page?deviceId=${deviceInfo?._id || ''}&customerId=${customerId}&orderId=${orderId}&rating=3"
                 style="display: inline-block; padding: 10px 15px; background-color: #ffc107; color: #333; text-decoration: none; border-radius: 5px; font-size: 18px; margin: 5px;">
                ⭐⭐⭐ (3 Stars)
              </a>
              <a href="${apiBaseUrl}/api/v1/device/review-page?deviceId=${deviceInfo?._id || ''}&customerId=${customerId}&orderId=${orderId}&rating=4"
                 style="display: inline-block; padding: 10px 15px; background-color: #ffc107; color: #333; text-decoration: none; border-radius: 5px; font-size: 18px; margin: 5px;">
                ⭐⭐⭐⭐ (4 Stars)
              </a>
              <a href="${apiBaseUrl}/api/v1/device/review-page?deviceId=${deviceInfo?._id || ''}&customerId=${customerId}&orderId=${orderId}&rating=5"
                 style="display: inline-block; padding: 10px 15px; background-color: #ffc107; color: #333; text-decoration: none; border-radius: 5px; font-size: 18px; margin: 5px;">
                ⭐⭐⭐⭐⭐ (5 Stars)
              </a>
            </div>
            <p style="font-size: 13px; color: #666; margin-top: 15px;">
              <em>Click on any star rating above to leave your review!</em>
            </p>
          </div>
        </div>

        <p>If you have any questions, feel free to reach out at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>

        <div class="footer">
          <p>Follow us:</p>
          <div class="social-icons">
            <a href="https://www.linkedin.com/company/patronworks/"><img src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690439086/WhatsApp_Image_2023-07-27_at_11.12.37_AM_1_whbn0t.jpg" alt="LinkedIn" /></a>
            <a href="https://www.facebook.com/patronworks"><img src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690439056/WhatsApp_Image_2023-07-27_at_11.12.37_AM_yedkyi.jpg" alt="Facebook" /></a>
          </div>
          <p>&copy; ${new Date().getFullYear()} ${businessName}. All rights reserved.</p>
        </div>
      </div>
    </body>
  </html>
  `;
};
